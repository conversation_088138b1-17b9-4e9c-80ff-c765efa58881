import './style.css';

// Import models
import { AudioRecordingModel } from './models/AudioRecordingModel.js';
import { AudioStorageModel } from './models/AudioStorageModel.js';
import { TextPromptModel } from './models/TextPromptModel.js';

// Import views
import { AudioRecordingView } from './views/AudioRecordingView.js';

// Import presenters
import { AudioRecordingPresenter } from './presenters/AudioRecordingPresenter.js';

/**
 * AureaVoice Application
 * Main application class that initializes the MVP architecture
 */
class AureaVoiceApp {
  constructor() {
    this.models = {};
    this.views = {};
    this.presenters = {};
    this.isInitialized = false;
  }

  /**
   * Initialize the application
   */
  async init() {
    if (this.isInitialized) {
      return;
    }

    try {
      // Initialize models
      this.initializeModels();

      // Initialize views
      this.initializeViews();

      // Initialize presenters
      await this.initializePresenters();

      // Setup global error handling
      this.setupErrorHandling();

      this.isInitialized = true;
      console.log('AureaVoice application initialized successfully');
    } catch (error) {
      console.error('Failed to initialize AureaVoice application:', error);
      this.showInitializationError(error);
    }
  }

  /**
   * Initialize all models
   */
  initializeModels() {
    this.models.audioRecording = new AudioRecordingModel();
    this.models.audioStorage = new AudioStorageModel();
    this.models.textPrompt = new TextPromptModel();
  }

  /**
   * Initialize all views
   */
  initializeViews() {
    const appElement = document.querySelector('#app');
    if (!appElement) {
      throw new Error('App element not found');
    }

    this.views.audioRecording = new AudioRecordingView(appElement);
  }

  /**
   * Initialize all presenters
   */
  async initializePresenters() {
    this.presenters.audioRecording = new AudioRecordingPresenter(
      this.models.audioRecording,
      this.models.audioStorage,
      this.models.textPrompt,
      this.views.audioRecording
    );

    await this.presenters.audioRecording.initialize();
  }

  /**
   * Setup global error handling
   */
  setupErrorHandling() {
    window.addEventListener('error', (event) => {
      console.error('Global error:', event.error);
      this.showError('An unexpected error occurred. Please refresh the page.');
    });

    window.addEventListener('unhandledrejection', (event) => {
      console.error('Unhandled promise rejection:', event.reason);
      this.showError('An unexpected error occurred. Please refresh the page.');
    });
  }

  /**
   * Show initialization error
   * @param {Error} error - Error object
   */
  showInitializationError(error) {
    const appElement = document.querySelector('#app');
    if (appElement) {
      appElement.innerHTML = `
        <div style="text-align: center; padding: 2rem; color: #ef4444;">
          <h1>⚠️ Initialization Error</h1>
          <p>Failed to initialize AureaVoice application.</p>
          <p><strong>Error:</strong> ${error.message}</p>
          <button onclick="location.reload()" style="
            padding: 0.75rem 1.5rem;
            background: #646cff;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            margin-top: 1rem;
          ">
            Reload Page
          </button>
        </div>
      `;
    }
  }

  /**
   * Show error message
   * @param {string} message - Error message
   */
  showError(message) {
    // Create or update global error display
    let errorElement = document.querySelector('.global-error');
    if (!errorElement) {
      errorElement = document.createElement('div');
      errorElement.className = 'global-error';
      errorElement.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: rgba(239, 68, 68, 0.9);
        color: white;
        padding: 1rem;
        border-radius: 8px;
        z-index: 1000;
        max-width: 300px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      `;
      document.body.appendChild(errorElement);
    }

    errorElement.textContent = message;
    errorElement.style.display = 'block';

    // Auto-hide after 5 seconds
    setTimeout(() => {
      if (errorElement) {
        errorElement.style.display = 'none';
      }
    }, 5000);
  }

  /**
   * Cleanup application
   */
  cleanup() {
    // Cleanup presenters
    Object.values(this.presenters).forEach(presenter => {
      if (presenter.cleanup) {
        presenter.cleanup();
      }
    });

    // Cleanup models
    Object.values(this.models).forEach(model => {
      if (model.cleanup) {
        model.cleanup();
      }
    });

    this.isInitialized = false;
  }
}

// Initialize application when DOM is loaded
document.addEventListener('DOMContentLoaded', async () => {
  const app = new AureaVoiceApp();
  await app.init();

  // Make app globally available for debugging
  window.aureaVoiceApp = app;
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
  if (window.aureaVoiceApp) {
    window.aureaVoiceApp.cleanup();
  }
});
