# AureaVoice - Audio Recording Web Application

AureaVoice adalah aplikasi web untuk merekam audio dengan kualitas tinggi menggunakan teknologi noise suppression dan echo cancellation. Aplikasi ini dibangun dengan arsitektur Model-View-Presenter (MVP) menggunakan Vite + Vanilla JavaScript dan Howler.js.

## Fitur Utama

- 🎤 **Recording Audio**: Rekam audio langsung dari microphone dengan noise suppression dan echo cancellation
- 📁 **Upload File**: Upload file audio yang sudah ada
- 🔄 **Audio Processing**: Konversi otomatis ke format WAV 16kHz
- 💾 **Local Storage**: Simpan recording di localStorage dengan nama file `rec-{uuid8}`
- ▶️ **Audio Playback**: Putar kembali recording menggunakan Howler.js
- 📝 **Text Prompts**: Tampilkan teks untuk dibaca user (mirip Common Voice dataset)
- 📱 **Responsive Design**: UI yang responsif untuk berbagai ukuran layar

## Teknologi yang Digunakan

- **Frontend Framework**: Vite + Vanilla JavaScript
- **Audio Library**: Howler.js untuk playback
- **Architecture**: Model-View-Presenter (MVP)
- **Styling**: CSS3 dengan custom properties
- **Audio Processing**: Web Audio API untuk konversi dan resampling

## Arsitektur MVP

### Models
- `AudioRecordingModel`: Menangani recording dan processing audio
- `AudioStorageModel`: Mengelola penyimpanan di localStorage
- `TextPromptModel`: Mengelola text prompts untuk user

### Views
- `AudioRecordingView`: UI untuk kontrol recording dan display recordings
- `BaseView`: Base class untuk semua view components

### Presenters
- `AudioRecordingPresenter`: Koordinasi antara models dan views
- `BasePresenter`: Base class untuk semua presenters

## Instalasi dan Menjalankan

1. **Clone repository**
   ```bash
   git clone <repository-url>
   cd av-frontend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Jalankan development server**
   ```bash
   npm run dev
   ```

4. **Buka browser**
   ```
   http://localhost:5173
   ```

## Cara Penggunaan

### Recording Audio
1. Klik tombol "🎤 Start Recording"
2. Izinkan akses microphone ketika diminta browser
3. Baca teks yang ditampilkan
4. Klik "⏹️ Stop Recording" untuk menghentikan
5. Audio akan diproses dan disimpan otomatis

### Upload File Audio
1. Klik tombol "📁 Upload Audio File"
2. Pilih file audio dari komputer
3. File akan diproses dan dikonversi ke WAV 16kHz

### Mengelola Recordings
- **Play**: Klik tombol "▶️ Play" untuk memutar recording
- **Download**: Klik tombol "💾 Download" untuk mengunduh file
- **Delete**: Klik tombol "🗑️ Delete" untuk menghapus recording

### Text Prompts
- Klik pada teks prompt untuk mendapatkan teks baru
- Teks dirancang mirip dengan dataset Common Voice

## Spesifikasi Audio

- **Format Output**: WAV
- **Sample Rate**: 16kHz
- **Channels**: Mono (1 channel)
- **Bit Depth**: 16-bit PCM
- **Audio Enhancements**:
  - Echo Cancellation: ✅
  - Noise Suppression: ✅
  - Auto Gain Control: ✅

## Struktur File

```
src/
├── data/
│   └── textPrompts.js          # Data teks prompts
├── models/
│   ├── BaseModel.js            # Base class untuk models
│   ├── AudioRecordingModel.js  # Model untuk recording audio
│   ├── AudioStorageModel.js    # Model untuk storage
│   └── TextPromptModel.js      # Model untuk text prompts
├── views/
│   ├── BaseView.js             # Base class untuk views
│   └── AudioRecordingView.js   # View untuk recording UI
├── presenters/
│   ├── BasePresenter.js        # Base class untuk presenters
│   └── AudioRecordingPresenter.js # Presenter utama
├── utils/
│   └── uuid.js                 # Utility untuk generate UUID
├── main.js                     # Entry point aplikasi
└── style.css                   # Styling CSS
```

## Browser Support

- Chrome 66+
- Firefox 60+
- Safari 11.1+
- Edge 79+

**Catatan**: Fitur recording memerlukan HTTPS atau localhost untuk akses microphone.

## Troubleshooting

### Microphone tidak bisa diakses
- Pastikan browser memiliki permission untuk mengakses microphone
- Pastikan aplikasi dijalankan di HTTPS atau localhost
- Cek apakah microphone tidak sedang digunakan aplikasi lain

### Audio tidak bisa diputar
- Pastikan browser mendukung format WAV
- Cek apakah Howler.js berhasil dimuat
- Periksa console browser untuk error

### Storage penuh
- Hapus beberapa recording lama
- Browser localStorage memiliki limit ~5-10MB
- Recording disimpan dalam format base64 yang lebih besar

## Development

### Menambah Text Prompts
Edit file `src/data/textPrompts.js` dan tambahkan teks baru ke array `textPrompts`.

### Mengubah Audio Settings
Edit konstanta di `AudioRecordingModel.js`:
- Sample rate
- Audio constraints
- Processing parameters

### Styling
Edit `src/style.css` untuk mengubah tampilan UI.

## License

MIT License - lihat file LICENSE untuk detail.

## Contributing

1. Fork repository
2. Buat feature branch
3. Commit changes
4. Push ke branch
5. Buat Pull Request

## Support

Jika ada masalah atau pertanyaan, silakan buat issue di repository ini.
