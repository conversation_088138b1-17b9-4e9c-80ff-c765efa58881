/**
 * Text prompts similar to Common Voice dataset
 * These are sample sentences for users to read during recording
 */
export const textPrompts = [
  "The quick brown fox jumps over the lazy dog.",
  "She sells seashells by the seashore.",
  "How much wood would a woodchuck chuck if a woodchuck could chuck wood?",
  "<PERSON> picked a peck of pickled peppers.",
  "A proper copper coffee pot.",
  "Red leather, yellow leather.",
  "Unique New York, unique New York.",
  "Toy boat, toy boat, toy boat.",
  "Six sick slick slim sycamore saplings.",
  "The sixth sick sheik's sixth sheep's sick.",
  "Technology is advancing rapidly in the modern world.",
  "Artificial intelligence is transforming various industries.",
  "Climate change requires immediate global action.",
  "Education is the foundation of a progressive society.",
  "Healthcare accessibility remains a critical challenge.",
  "Renewable energy sources are becoming more efficient.",
  "Digital communication has revolutionized human interaction.",
  "Scientific research drives innovation and discovery.",
  "Cultural diversity enriches our global community.",
  "Economic sustainability requires balanced policies.",
  "The sun rises in the east and sets in the west.",
  "Water is essential for all forms of life on Earth.",
  "Music has the power to unite people across cultures.",
  "Reading books expands knowledge and imagination.",
  "Exercise and proper nutrition promote good health.",
  "Friendship is one of life's greatest treasures.",
  "Learning new languages opens doors to opportunities.",
  "Nature provides beauty and resources for humanity.",
  "Creativity and innovation drive human progress.",
  "Kindness and compassion make the world better.",
  "The library is a quiet place for studying and research.",
  "Fresh vegetables and fruits are important for nutrition.",
  "Public transportation helps reduce traffic congestion.",
  "Recycling helps protect our environment for future generations.",
  "Regular exercise improves both physical and mental health.",
  "Good communication skills are essential in the workplace.",
  "Time management is crucial for achieving personal goals.",
  "Teamwork and collaboration lead to better results.",
  "Continuous learning keeps the mind sharp and engaged.",
  "Respect and understanding foster positive relationships."
];

/**
 * Get a random text prompt
 * @returns {string} Random text prompt
 */
export function getRandomPrompt() {
  const randomIndex = Math.floor(Math.random() * textPrompts.length);
  return textPrompts[randomIndex];
}

/**
 * Get multiple random prompts
 * @param {number} count - Number of prompts to return
 * @returns {string[]} Array of random text prompts
 */
export function getRandomPrompts(count = 5) {
  const shuffled = [...textPrompts].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
}
