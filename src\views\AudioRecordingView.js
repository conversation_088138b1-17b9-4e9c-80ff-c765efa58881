import { BaseView } from './BaseView.js';

/**
 * Audio Recording View
 * Handles UI for audio recording functionality
 */
export class AudioRecordingView extends BaseView {
  constructor(element) {
    super(element);
    this.recordButton = null;
    this.stopButton = null;
    this.fileInput = null;
    this.textPrompt = null;
    this.recordingIndicator = null;
    this.processingIndicator = null;
    this.recordingsList = null;
    this.initializeElements();
  }

  /**
   * Initialize DOM elements
   */
  initializeElements() {
    this.recordButton = this.querySelector('#record-btn');
    this.stopButton = this.querySelector('#stop-btn');
    this.fileInput = this.querySelector('#file-input');
    this.textPrompt = this.querySelector('#text-prompt');
    this.recordingIndicator = this.querySelector('#recording-indicator');
    this.processingIndicator = this.querySelector('#processing-indicator');
    this.recordingsList = this.querySelector('#recordings-list');
  }

  /**
   * Set up event handlers
   * @param {Object} handlers - Event handler functions
   */
  setupEventHandlers(handlers) {
    if (this.recordButton && handlers.onRecordClick) {
      this.addEventListener(this.recordButton, 'click', handlers.onRecordClick);
    }

    if (this.stopButton && handlers.onStopClick) {
      this.addEventListener(this.stopButton, 'click', handlers.onStopClick);
    }

    if (this.fileInput && handlers.onFileSelect) {
      this.addEventListener(this.fileInput, 'change', handlers.onFileSelect);
    }

    if (this.textPrompt && handlers.onPromptClick) {
      this.addEventListener(this.textPrompt, 'click', handlers.onPromptClick);
    }
  }

  /**
   * Update text prompt
   * @param {string} text - Prompt text
   */
  updateTextPrompt(text) {
    this.setText(this.textPrompt, text);
  }

  /**
   * Show recording started state
   */
  showRecordingStarted() {
    this.setEnabled(this.recordButton, false);
    this.setEnabled(this.stopButton, true);
    this.setEnabled(this.fileInput, false);
    this.show(this.recordingIndicator);
    this.hide(this.processingIndicator);
    
    this.setText(this.recordButton, '🎤 Recording...');
    this.recordButton.classList.add('recording');
  }

  /**
   * Show recording stopped state
   */
  showRecordingStopped() {
    this.setEnabled(this.recordButton, true);
    this.setEnabled(this.stopButton, false);
    this.setEnabled(this.fileInput, true);
    this.hide(this.recordingIndicator);
    
    this.setText(this.recordButton, '🎤 Start Recording');
    this.recordButton.classList.remove('recording');
  }

  /**
   * Show processing state
   */
  showProcessing() {
    this.setEnabled(this.recordButton, false);
    this.setEnabled(this.stopButton, false);
    this.setEnabled(this.fileInput, false);
    this.hide(this.recordingIndicator);
    this.show(this.processingIndicator);
  }

  /**
   * Hide processing state
   */
  hideProcessing() {
    this.hide(this.processingIndicator);
    this.setEnabled(this.recordButton, true);
    this.setEnabled(this.fileInput, true);
  }

  /**
   * Show error message
   * @param {string} message - Error message
   */
  showError(message) {
    // Create or update error display
    let errorElement = this.querySelector('.error-message');
    if (!errorElement) {
      errorElement = document.createElement('div');
      errorElement.className = 'error-message';
      this.querySelector('#recording-controls').appendChild(errorElement);
    }
    
    errorElement.textContent = message;
    errorElement.style.display = 'block';
    
    // Auto-hide after 5 seconds
    setTimeout(() => {
      if (errorElement) {
        errorElement.style.display = 'none';
      }
    }, 5000);
  }

  /**
   * Update recordings list
   * @param {Array} recordings - Array of recording objects
   */
  updateRecordingsList(recordings) {
    if (!this.recordingsList) return;

    if (recordings.length === 0) {
      this.setHTML(this.recordingsList, 
        '<p class="no-recordings">No recordings yet. Start recording to see your audio files here.</p>'
      );
      return;
    }

    const recordingsHTML = recordings.map(recording => this.createRecordingHTML(recording)).join('');
    this.setHTML(this.recordingsList, recordingsHTML);

    // Add event listeners for recording controls
    this.setupRecordingControls();
  }

  /**
   * Create HTML for a single recording
   * @param {Object} recording - Recording object
   * @returns {string} HTML string
   */
  createRecordingHTML(recording) {
    const date = new Date(recording.timestamp).toLocaleString();
    const duration = recording.duration ? `${recording.duration.toFixed(1)}s` : 'Unknown';
    const size = this.formatFileSize(recording.size);
    const type = recording.isUpload ? 'Uploaded' : 'Recorded';

    return `
      <div class="recording-item" data-recording-id="${recording.id}">
        <div class="recording-info">
          <div class="recording-title">
            <span class="recording-name">${recording.filename}</span>
            <span class="recording-type">${type}</span>
          </div>
          <div class="recording-details">
            <span class="recording-date">${date}</span>
            <span class="recording-duration">${duration}</span>
            <span class="recording-size">${size}</span>
          </div>
        </div>
        <div class="recording-controls">
          <button class="btn btn-small play-btn" data-action="play" data-recording-id="${recording.id}">
            ▶️ Play
          </button>
          <button class="btn btn-small download-btn" data-action="download" data-recording-id="${recording.id}">
            💾 Download
          </button>
          <button class="btn btn-small btn-danger delete-btn" data-action="delete" data-recording-id="${recording.id}">
            🗑️ Delete
          </button>
        </div>
      </div>
    `;
  }

  /**
   * Setup event handlers for recording controls
   */
  setupRecordingControls() {
    // Play buttons
    this.querySelectorAll('.play-btn').forEach(btn => {
      this.addEventListener(btn, 'click', (e) => {
        const recordingId = e.target.dataset.recordingId;
        this.element.dispatchEvent(new CustomEvent('playRecording', { 
          detail: { recordingId } 
        }));
      });
    });

    // Download buttons
    this.querySelectorAll('.download-btn').forEach(btn => {
      this.addEventListener(btn, 'click', (e) => {
        const recordingId = e.target.dataset.recordingId;
        this.element.dispatchEvent(new CustomEvent('downloadRecording', { 
          detail: { recordingId } 
        }));
      });
    });

    // Delete buttons
    this.querySelectorAll('.delete-btn').forEach(btn => {
      this.addEventListener(btn, 'click', (e) => {
        const recordingId = e.target.dataset.recordingId;
        if (confirm('Are you sure you want to delete this recording?')) {
          this.element.dispatchEvent(new CustomEvent('deleteRecording', { 
            detail: { recordingId } 
          }));
        }
      });
    });
  }

  /**
   * Update play button state
   * @param {string} recordingId - Recording ID
   * @param {boolean} isPlaying - Whether audio is playing
   */
  updatePlayButton(recordingId, isPlaying) {
    const playBtn = this.querySelector(`[data-recording-id="${recordingId}"][data-action="play"]`);
    if (playBtn) {
      playBtn.textContent = isPlaying ? '⏸️ Pause' : '▶️ Play';
    }
  }

  /**
   * Format file size for display
   * @param {number} bytes - Size in bytes
   * @returns {string} Formatted size string
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Show success message
   * @param {string} message - Success message
   */
  showSuccess(message) {
    // Create or update success display
    let successElement = this.querySelector('.success-message');
    if (!successElement) {
      successElement = document.createElement('div');
      successElement.className = 'success-message';
      this.querySelector('#recording-controls').appendChild(successElement);
    }
    
    successElement.textContent = message;
    successElement.style.display = 'block';
    
    // Auto-hide after 3 seconds
    setTimeout(() => {
      if (successElement) {
        successElement.style.display = 'none';
      }
    }, 3000);
  }

  /**
   * Clear file input
   */
  clearFileInput() {
    if (this.fileInput) {
      this.fileInput.value = '';
    }
  }
}
