/**
 * Base Model class for MVP architecture
 * Models handle data and business logic without knowing about the view
 */
export class BaseModel {
  constructor() {
    this.observers = [];
  }

  /**
   * Add observer to be notified of model changes
   * @param {Function} observer - Callback function to notify
   */
  addObserver(observer) {
    this.observers.push(observer);
  }

  /**
   * Remove observer
   * @param {Function} observer - Observer to remove
   */
  removeObserver(observer) {
    this.observers = this.observers.filter(obs => obs !== observer);
  }

  /**
   * Notify all observers of model changes
   * @param {string} event - Event type
   * @param {*} data - Event data
   */
  notifyObservers(event, data) {
    this.observers.forEach(observer => observer(event, data));
  }
}
