/**
 * Base View class for MVP architecture
 * Views handle DOM manipulation and user interface
 */
export class BaseView {
  constructor(element) {
    this.element = element;
    this.eventHandlers = new Map();
  }

  /**
   * Add event listener to an element
   * @param {string} selector - CSS selector or element ID
   * @param {string} event - Event type (click, change, etc.)
   * @param {Function} handler - Event handler function
   */
  addEventListener(selector, event, handler) {
    const element = typeof selector === 'string' 
      ? this.element.querySelector(selector) 
      : selector;
    
    if (element) {
      element.addEventListener(event, handler);
      
      // Store for cleanup
      const key = `${selector}-${event}`;
      if (!this.eventHandlers.has(key)) {
        this.eventHandlers.set(key, []);
      }
      this.eventHandlers.get(key).push({ element, handler });
    }
  }

  /**
   * Remove all event listeners
   */
  removeAllEventListeners() {
    this.eventHandlers.forEach((handlers, key) => {
      const [selector, event] = key.split('-');
      handlers.forEach(({ element, handler }) => {
        element.removeEventListener(event, handler);
      });
    });
    this.eventHandlers.clear();
  }

  /**
   * Find element within this view's scope
   * @param {string} selector - CSS selector
   * @returns {Element|null}
   */
  querySelector(selector) {
    return this.element.querySelector(selector);
  }

  /**
   * Find all elements within this view's scope
   * @param {string} selector - CSS selector
   * @returns {NodeList}
   */
  querySelectorAll(selector) {
    return this.element.querySelectorAll(selector);
  }

  /**
   * Show element
   * @param {string|Element} selector - CSS selector or element
   */
  show(selector) {
    const element = typeof selector === 'string' 
      ? this.querySelector(selector) 
      : selector;
    if (element) {
      element.classList.remove('hidden');
    }
  }

  /**
   * Hide element
   * @param {string|Element} selector - CSS selector or element
   */
  hide(selector) {
    const element = typeof selector === 'string' 
      ? this.querySelector(selector) 
      : selector;
    if (element) {
      element.classList.add('hidden');
    }
  }

  /**
   * Toggle element visibility
   * @param {string|Element} selector - CSS selector or element
   */
  toggle(selector) {
    const element = typeof selector === 'string' 
      ? this.querySelector(selector) 
      : selector;
    if (element) {
      element.classList.toggle('hidden');
    }
  }

  /**
   * Set text content of element
   * @param {string|Element} selector - CSS selector or element
   * @param {string} text - Text content
   */
  setText(selector, text) {
    const element = typeof selector === 'string' 
      ? this.querySelector(selector) 
      : selector;
    if (element) {
      element.textContent = text;
    }
  }

  /**
   * Set HTML content of element
   * @param {string|Element} selector - CSS selector or element
   * @param {string} html - HTML content
   */
  setHTML(selector, html) {
    const element = typeof selector === 'string' 
      ? this.querySelector(selector) 
      : selector;
    if (element) {
      element.innerHTML = html;
    }
  }

  /**
   * Enable/disable element
   * @param {string|Element} selector - CSS selector or element
   * @param {boolean} enabled - Whether element should be enabled
   */
  setEnabled(selector, enabled) {
    const element = typeof selector === 'string' 
      ? this.querySelector(selector) 
      : selector;
    if (element) {
      element.disabled = !enabled;
    }
  }
}
