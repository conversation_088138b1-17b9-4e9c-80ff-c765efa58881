/**
 * Base Presenter class for MVP architecture
 * Presenters coordinate between models and views
 */
export class BasePresenter {
  constructor(model, view) {
    this.model = model;
    this.view = view;
    this.isInitialized = false;
  }

  /**
   * Initialize the presenter
   * Sets up model observers and view event handlers
   */
  initialize() {
    if (this.isInitialized) {
      return;
    }

    // Set up model observers
    this.setupModelObservers();
    
    // Set up view event handlers
    this.setupViewEventHandlers();
    
    this.isInitialized = true;
  }

  /**
   * Setup model observers - to be implemented by subclasses
   */
  setupModelObservers() {
    // Override in subclasses
  }

  /**
   * Setup view event handlers - to be implemented by subclasses
   */
  setupViewEventHandlers() {
    // Override in subclasses
  }

  /**
   * Handle model events - to be implemented by subclasses
   * @param {string} event - Event type
   * @param {*} data - Event data
   */
  handleModelEvent(event, data) {
    // Override in subclasses
  }

  /**
   * Cleanup presenter
   * Removes observers and event handlers
   */
  cleanup() {
    if (this.view && this.view.removeAllEventListeners) {
      this.view.removeAllEventListeners();
    }
    this.isInitialized = false;
  }
}
