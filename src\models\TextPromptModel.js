import { BaseModel } from './BaseModel.js';
import { getRandomPrompt, getRandomPrompts } from '../data/textPrompts.js';

/**
 * Text Prompt Model
 * Handles text prompts for users to read during recording
 */
export class TextPromptModel extends BaseModel {
  constructor() {
    super();
    this.currentPrompt = '';
    this.promptHistory = [];
    this.maxHistorySize = 50;
  }

  /**
   * Get a new random text prompt
   * @returns {string} Text prompt
   */
  getNewPrompt() {
    let newPrompt;
    let attempts = 0;
    const maxAttempts = 10;

    // Try to get a prompt that's different from recent ones
    do {
      newPrompt = getRandomPrompt();
      attempts++;
    } while (
      this.promptHistory.includes(newPrompt) && 
      attempts < maxAttempts
    );

    this.currentPrompt = newPrompt;
    this.addToHistory(newPrompt);
    
    this.notifyObservers('promptChanged', { 
      prompt: newPrompt,
      isNew: true 
    });
    
    return newPrompt;
  }

  /**
   * Get current prompt
   * @returns {string} Current text prompt
   */
  getCurrentPrompt() {
    if (!this.currentPrompt) {
      return this.getNewPrompt();
    }
    return this.currentPrompt;
  }

  /**
   * Get multiple random prompts
   * @param {number} count - Number of prompts to get
   * @returns {string[]} Array of text prompts
   */
  getMultiplePrompts(count = 5) {
    const prompts = getRandomPrompts(count);
    this.notifyObservers('multiplePromptsGenerated', { prompts, count });
    return prompts;
  }

  /**
   * Add prompt to history
   * @param {string} prompt - Prompt to add to history
   */
  addToHistory(prompt) {
    // Add to beginning of history
    this.promptHistory.unshift(prompt);
    
    // Keep history size manageable
    if (this.promptHistory.length > this.maxHistorySize) {
      this.promptHistory = this.promptHistory.slice(0, this.maxHistorySize);
    }
  }

  /**
   * Get prompt history
   * @returns {string[]} Array of recent prompts
   */
  getHistory() {
    return [...this.promptHistory];
  }

  /**
   * Clear prompt history
   */
  clearHistory() {
    this.promptHistory = [];
    this.notifyObservers('historyCleared', {});
  }

  /**
   * Set custom prompt
   * @param {string} prompt - Custom prompt text
   */
  setCustomPrompt(prompt) {
    if (typeof prompt !== 'string' || prompt.trim().length === 0) {
      this.notifyObservers('error', { 
        message: 'Invalid prompt text.' 
      });
      return false;
    }

    this.currentPrompt = prompt.trim();
    this.addToHistory(this.currentPrompt);
    
    this.notifyObservers('promptChanged', { 
      prompt: this.currentPrompt,
      isCustom: true 
    });
    
    return true;
  }

  /**
   * Get prompt statistics
   * @returns {Object} Prompt statistics
   */
  getStats() {
    const currentLength = this.currentPrompt.length;
    const avgHistoryLength = this.promptHistory.length > 0 
      ? this.promptHistory.reduce((sum, prompt) => sum + prompt.length, 0) / this.promptHistory.length
      : 0;

    return {
      currentPromptLength: currentLength,
      historyCount: this.promptHistory.length,
      averagePromptLength: Math.round(avgHistoryLength),
      maxHistorySize: this.maxHistorySize
    };
  }
}
