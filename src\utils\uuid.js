/**
 * Generate a random UUID with specified length
 * @param {number} length - Length of UUID (default: 8)
 * @returns {string} Random UUID string
 */
export function generateUUID(length = 8) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  
  return result;
}

/**
 * Generate recording filename with rec- prefix and 8-character UUID
 * @returns {string} Recording filename
 */
export function generateRecordingFilename() {
  return `rec-${generateUUID(8)}`;
}
